package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.dto.request.CategoryReq;
import tndung.vnfb.smm.dto.request.CategoryReorderReq;
import tndung.vnfb.smm.dto.request.EditCategoryReq;
import tndung.vnfb.smm.dto.response.category.CategoryRes;
import tndung.vnfb.smm.entity.Category;
import tndung.vnfb.smm.entity.Platform;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.mapper.CategoryMapper;
import tndung.vnfb.smm.repository.tenant.CategoryRepository;
import tndung.vnfb.smm.repository.tenant.PlatformRepository;
import tndung.vnfb.smm.service.CategoryService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {
    private final CategoryRepository categoryRepository;
    private final CategoryMapper categoryMapper;
    private final PlatformRepository platformRepository;

    @Override
    public CategoryRes deactivate(Long id) {
        final Category category = findById(id);
        category.setStatus(CommonStatus.DEACTIVATED);
        return categoryMapper.toRes(categoryRepository.save(category));
    }

    @Override
    public CategoryRes active(Long id) {
        final Category category = findById(id);
        category.setStatus(CommonStatus.ACTIVATED);
        return categoryMapper.toRes(categoryRepository.save(category));
    }

    @Override
    public CategoryRes edit(Long id, EditCategoryReq req) {
        final Category category = findById(id);

        final Platform platform = platformRepository.findById(req.getPlatformId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.PLATFORM_NOT_EXISTS));

        if (!req.getName().equals(category.getName()) && categoryRepository.findByName(req.getName().trim()).isPresent())
            throw new InvalidParameterException(IdErrorCode.CATEGORY_NAME_EXISTS);
        category.setPlatform(platform);
        category.setName(req.getName());

        return categoryMapper.toRes(categoryRepository.save(category));
    }

    @Override
    public Category findById(Long id) {
        return categoryRepository.findById(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));
    }

    @Override
    public CategoryRes add(CategoryReq req) {

        final Optional<Category> categoryOptional = categoryRepository.findByName(req.getName().trim());
        if (categoryOptional.isPresent()) throw new InvalidParameterException(IdErrorCode.CATEGORY_NAME_EXISTS);

        final Category category = categoryMapper.toEntity(req);
        category.setPlatform(new Platform(req.getPlatformId()));
        if (req.getStatus() != null) {
            category.setStatus(req.getStatus());
        } else {
            category.setStatus(CommonStatus.ACTIVATED);
        }

        Integer maxSort = categoryRepository.findMaxSort();
        if(maxSort == null) maxSort = 0;
        category.setSort(maxSort + 1);
        return categoryMapper.toRes(categoryRepository.save(category));
    }

    @Override
    public List<CategoryRes> getAll() {
        return categoryMapper.toRes(categoryRepository.findAll());
    }

    @Override
    public List<CategoryRes> getByPlatform(Long platform) {
        return categoryMapper.toRes(categoryRepository.findAllByPlatformOrderBySortAsc(new Platform(platform)));
    }

    @Override
    @Transactional
    public void swapSort(Long id1, Long id2) {
        final Category category1 = categoryRepository.findById(id1)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));


        final Category category2 = categoryRepository.findById(id2)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));

        // Lấy giá trị sort hiện tại
        Integer sort1 = category1.getSort();
        Integer sort2 = category2.getSort();

        // Hoán đổi giá trị sort
        category1.setSort(sort2);
        category2.setSort(sort1);
        categoryRepository.saveAll(Arrays.asList(category1, category2));
    }

    @Override
    @Transactional
    public void reorder(CategoryReorderReq req) {
        // Validate that all categories exist and collect them
        List<Category> categoriesToUpdate = new ArrayList<>();

        for (CategoryReorderReq.CategoryOrderItem item : req.getCategories()) {
            Category category = categoryRepository.findById(item.getId())
                    .orElseThrow(() -> new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));

            // Update the sort order
            category.setSort(item.getSort());
            categoriesToUpdate.add(category);
        }

        // Save all updated categories
        categoryRepository.saveAll(categoriesToUpdate);
    }

    @Override
    @Transactional
    public void changePlatform(Long categoryId, Long newPlatformId) {
        final Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() ->new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));

        final Platform newPlatform = platformRepository.findById(newPlatformId)
                .orElseThrow(() ->new InvalidParameterException(IdErrorCode.PLATFORM_NOT_EXISTS));

        category.setPlatform(newPlatform);
        categoryRepository.save(category);
    }
}
