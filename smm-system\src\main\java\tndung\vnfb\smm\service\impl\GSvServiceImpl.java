package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;


import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.constant.enums.UpdateLogStatus;
import tndung.vnfb.smm.dto.request.ServiceReq;
import tndung.vnfb.smm.dto.request.ServiceReorderReq;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.service.SuperServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes;
import tndung.vnfb.smm.entity.ApiProvider;
import tndung.vnfb.smm.entity.Category;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.entity.UpdateLog;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.mapper.GSvMapper;
import tndung.vnfb.smm.repository.tenant.ApiProviderRepository;
import tndung.vnfb.smm.repository.tenant.CategoryRepository;
import tndung.vnfb.smm.repository.tenant.GSvRepository;
import tndung.vnfb.smm.rest.SMMConsumer;
import tndung.vnfb.smm.service.GSvService;
import tndung.vnfb.smm.service.UpdateLogService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.ArrayList;


@Service
@RequiredArgsConstructor
public class GSvServiceImpl implements GSvService {
    private final CategoryRepository categoryRepository;
    private final GSvRepository gSvRepository;
    private final ApiProviderRepository apiProviderRepository;
    private final GSvMapper gSvMapper;
    private final SMMConsumer smmConsumer;
    private final UpdateLogService updateLogService;
    private static final BigDecimal ONE_HUNDRED = new BigDecimal(100);
    @Override
    public List<SMMServiceRes> getSMMServices() {
        return gSvMapper.toSMM(gSvRepository.findAllSMMByStatus(CommonStatus.ACTIVATED));
    }

    @Override
    public List<SMMServiceRes> getServicesByProvider(Long providerId) {
        final ApiProvider apiProvider = apiProviderRepository
                .findById(providerId).orElseThrow(() -> new InvalidParameterException(IdErrorCode.PROVIDER_NOT_EXISTS));
        return smmConsumer.getServices(apiProvider);
    }

    @Override
    public List<ServiceRes> getAll() {
        return gSvMapper.toRes(gSvRepository.findAll());
    }


//    private List<ServiceResponseDto> mapServicesToResponseDtos(List<Service> services, Long userId) {
//        // If user is not authenticated, simply return services without favorite status
//        if (userId == null) {
//            return services.stream()
//                    .map(this::mapToDto)
//                    .collect(Collectors.toList());
//        }
//
//        // Get all favorited service IDs for this user for efficient checking
//        List<Long> favoriteServiceIds = favoriteServiceRepository.findServiceIdsByUserId(userId);
//
//        // Map services to DTOs with favorite status
//        return services.stream()
//                .map(service -> {
//                    ServiceResponseDto dto = mapToDto(service);
//                    dto.setIsFavorite(favoriteServiceIds.contains(service.getId()));
//                    return dto;
//                })
//                .collect(Collectors.toList());
//    }


    @Override
    public List<SuperServiceRes> getSuperAll() {
        return gSvMapper.toSuperRes(gSvRepository.findAll());
    }

    @Override
    @Transactional
    public ServiceRes add(ServiceReq req) {
//        final Optional<GService> generalServiceOpt = gSvRepository.findByName(req.getName());
//        if (generalServiceOpt.isPresent()) throw new InvalidParameterException(IdErrorCode.SERVICE_NAME_EXISTS);


        final GService service = gSvMapper.toEntity(req);
        if (Boolean.FALSE.equals(req.getIsFixedPrice())) {
            service.setPrice(CommonHelper.extend(service.getOriginalPrice(), req.getPercent()));
        }

        if (!Objects.isNull(req.getApiProviderId())) {
            service.setApiProvider(new ApiProvider(req.getApiProviderId()));
        }

        // Set the category before incrementing sort values of existing services
        service.setCategory(new Category(req.getCategoryId()));

        // Get all services in the same category
        List<GService> categoryServices = gSvRepository.findAllByCategoryId(req.getCategoryId());

        // Increment the sort value of all existing services in the category
        if (!categoryServices.isEmpty()) {
            categoryServices.forEach(existingService -> {
                existingService.setSort(existingService.getSort() + 1);
            });
            gSvRepository.saveAll(categoryServices);
        }

        // Set the new service's sort value to 1 so it appears at the top
        service.setSort(1);

        final GService serviceSaved = gSvRepository.save(service);

        updateLogService.add(UpdateLog.builder()
                .service(
                        CommonHelper.getServiceDetail(service))
                .status(UpdateLogStatus.NEW).build());
        return gSvMapper.toRes(serviceSaved);
    }

    private UpdateLog buildLog(GService service, BigDecimal lastPrice)  {
        int compare = lastPrice.compareTo(service.getPrice());
        if (compare ==0) return null;
        UpdateLogStatus updateLogStatus;
        if (compare >0 ) {
            updateLogStatus = UpdateLogStatus.PRICE_DECREASE;
        } else {
            updateLogStatus =  UpdateLogStatus.PRICE_INCREASE;
        }


       return UpdateLog.builder()
                .service(
                        CommonHelper.getServiceDetail(service))
                .priceChangeFrom(lastPrice)
                .priceChangeTo(service.getPrice())
                .status(updateLogStatus).build();
    }

    @Override
    @Transactional
    public ServiceRes edit(Long id, ServiceReq req) {
        final GService service = gSvRepository.findByIdAndIsDeletedFalse(id)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));


        final BigDecimal lastPrice = service.getPrice();
        gSvMapper.update(req, service);
        if (Boolean.FALSE.equals(req.getIsFixedPrice())) {
            service.setPrice(CommonHelper.extend(service.getOriginalPrice(), req.getPercent()));
        }

        UpdateLog updateLog = buildLog(service, lastPrice);
        if (updateLog != null) updateLogService.add(updateLog);


        return gSvMapper.toRes(gSvRepository.save(service));
    }


    @Override
    public List<ServiceRes> getByCategory(Long categoryId) {

        return gSvMapper.toRes(gSvRepository.findAllByCategoryId(categoryId));
    }

    @Override
    public SuperServiceRes getResById(Long serviceId) {
        final GService generalService = gSvRepository.findByIdAndIsDeletedFalse(serviceId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.RESOURCE_ERROR));

        return gSvMapper.toSuperRes(generalService);
    }

    @Override
    public GService getById(Long serviceId) {
        return gSvRepository.findByIdAndIsDeletedFalse(serviceId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));
    }

    @Override
    public void swapSort(Long id1, Long id2) {
        final GService service1 = gSvRepository.findByIdAndIsDeletedFalse(id1)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));


        final GService service2 = gSvRepository.findByIdAndIsDeletedFalse(id2)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

        // Lấy giá trị sort hiện tại
        Integer sort1 = service1.getSort();
        Integer sort2 = service2.getSort();

        // Hoán đổi giá trị sort
        service1.setSort(sort2);
        service2.setSort(sort1);
        gSvRepository.saveAll(Arrays.asList(service1, service2));
    }

    @Override
    @Transactional
    public void reorder(ServiceReorderReq req) {
        // Validate that all services exist and collect them
        List<GService> servicesToUpdate = new ArrayList<>();

        for (ServiceReorderReq.ServiceOrderItem item : req.getServices()) {
            GService service = gSvRepository.findByIdAndIsDeletedFalse(item.getId())
                    .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

            // Update the sort order
            service.setSort(item.getSort());
            servicesToUpdate.add(service);
        }

        // Save all updated services
        gSvRepository.saveAll(servicesToUpdate);
    }

    @Override
    public void changePrice(Long id, BigDecimal percent) {
        final GService service = gSvRepository.findByIdAndIsDeletedFalse(id)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

        service.setPercent(percent);
        service.setIsDeleted(false);
        final BigDecimal lastPrice = service.getPrice();
        service.setPrice(CommonHelper.extend(service.getOriginalPrice(),percent));
        UpdateLog updateLog = buildLog(service, lastPrice);
        if (updateLog != null) updateLogService.add(updateLog);
        gSvRepository.save(service);
    }

    @Override
    public SuperServiceRes deactivate(Long id) {
        final GService service = findById(id);
        service.setStatus(CommonStatus.DEACTIVATED);

        updateLogService.add(UpdateLog.builder()
                .service(
                        CommonHelper.getServiceDetail(service))
                .status(UpdateLogStatus.OFF).build());

        return gSvMapper.toSuperRes(gSvRepository.save(service));
    }

    @Override
    public SuperServiceRes active(Long id) {
        final GService service = findById(id);
        service.setStatus(CommonStatus.ACTIVATED);

        updateLogService.add(UpdateLog.builder()
                .service(
                        CommonHelper.getServiceDetail(service))
                .status(UpdateLogStatus.ON).build());
        return gSvMapper.toSuperRes(gSvRepository.save(service));
    }

    @Override
    public GService findById(Long id) {
        return gSvRepository.findById(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));
    }

    @Override
    @Transactional
    public void changeCategory(Long serviceId, Long newCategory) {
        final GService service = gSvRepository.findByIdAndIsDeletedFalse(serviceId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

        final Category category = categoryRepository.findById(newCategory)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));


        service.setCategory(category);
        gSvRepository.save(service);
    }

    @Override
    @Transactional
    public void priceSort(Long categoryId, Sort.Direction direction) {
        final Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.CATEGORY_NOT_EXISTS));

        List<GService> gServices = category.getServices();

        // 1. First save the original order of sort values
        List<Integer> originalSortValues = gServices.stream()
                .map(GService::getSort)
                .toList();

        // 2. Sort services by price (either ascending or descending)
        if (direction.isAscending()) {
            gServices.sort(Comparator.comparing(GService::getPrice));
        } else {
            gServices.sort(Comparator.comparing(GService::getPrice).reversed());
        }

        // 3. Update the sort values in the sorted list
        for (int i = 0; i < gServices.size(); i++) {
            gServices.get(i).setSort(originalSortValues.get(i));
        }

        // 4. Save the updated services
        gSvRepository.saveAll(gServices);
    }

    @Override
    @Transactional
    public SuperServiceRes duplicate(Long serviceId) {
        final GService originalService = gSvRepository.findByIdAndIsDeletedFalse(serviceId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));

        final GService gService = new GService();

        // Set the category first
        Category category = originalService.getCategory();
        gService.setCategory(category);

        // Get all services in the same category
        List<GService> categoryServices = gSvRepository.findAllByCategoryId(category.getId());

        // Increment the sort value of all existing services in the category
        if (!categoryServices.isEmpty()) {
            categoryServices.forEach(existingService -> {
                existingService.setSort(existingService.getSort() + 1);
            });
            gSvRepository.saveAll(categoryServices);
        }

        // Set the new service's sort value to 1 so it appears at the top
        gService.setSort(1);

        // Copy all other properties from the original service
        gService.setApiServiceId(originalService.getApiServiceId());
        gService.setApiProvider(originalService.getApiProvider());
        gService.setDescription(originalService.getDescription());
        gService.setAddType(originalService.getAddType());

        gService.setAutoSync(originalService.getAutoSync());
        gService.setSyncMinMax(originalService.getSyncMinMax());
        gService.setSyncRefill(originalService.getSyncRefill());
        gService.setSyncCancel(originalService.getSyncCancel());
        gService.setSyncStatus(originalService.getSyncStatus());
        gService.setDripFeed(originalService.getDripFeed());
        gService.setLabels(originalService.getLabels());

        gService.setAverageTime(originalService.getAverageTime());
        gService.setLimitFrom(originalService.getLimitFrom());
        gService.setLimitTo(originalService.getLimitTo());

        gService.setName(originalService.getName());

        gService.setType(originalService.getType());
        gService.setRefillDays(originalService.getRefillDays());

        gService.setPrice(originalService.getPrice());
        gService.setPrice1(originalService.getPrice1());
        gService.setPrice2(originalService.getPrice2());

        gService.setMin(originalService.getMin());
        gService.setMax(originalService.getMax());

        gService.setOriginalPrice(originalService.getOriginalPrice());

        gService.setPercent(originalService.getPercent());
        gService.setPercent1(originalService.getPercent1());
        gService.setPercent2(originalService.getPercent2());

        return gSvMapper.toSuperRes(gSvRepository.save(gService));
    }

    @Override
    public void delete(Long id) {
        final GService service = gSvRepository.findByIdAndIsDeletedFalse(id)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.SERVICE_NOT_EXISTS));
        service.setIsDeleted(true);
        gSvRepository.save(service);
    }


}
