import { Injectable } from '@angular/core';
import { AdminServiceService } from './admin-service.service';
import { SuperGeneralSvRes } from '../../model/response/super-general-sv.model';
import { ExtendedCategoryRes } from '../../model/extended/extended-category.model';
import { CategoryService } from './category.service';

@Injectable({
  providedIn: 'root'
})
export class DragDropService {
  constructor(
    private adminService: AdminServiceService,
    private categoryService: CategoryService
  ) {}

  /**
   * Handles the drop of a service
   * @param sourceCategoryId The source category ID
   * @param targetCategoryId The target category ID
   * @param sourceIndex The source index
   * @param targetIndex The target index
   * @param callback Callback function to execute after completion
   */
  handleServiceDrop(
    sourceCategoryId: string,
    targetCategoryId: string,
    sourceIndex: number,
    targetIndex: number,
    callback?: () => void
  ): void {
    console.log(`Moving service from category ${sourceCategoryId}, index ${sourceIndex} to category ${targetCategoryId}, index ${targetIndex}`);

    try {
      // If source and target categories are the same, reorder within the category
      if (sourceCategoryId === targetCategoryId) {
        // Don't do anything if dropping at the same position
        if (sourceIndex === targetIndex) {
          console.log('Dropping at same position, no action needed');
          if (callback) callback();
          return;
        }

        // Get the current services by category
        const currentServicesByCategory = {...this.adminService.getServicesByCategoryValue()};

        // Get the services for the specified category
        const categoryServices = [...(currentServicesByCategory[sourceCategoryId] || [])];

        // Get the service to move
        const serviceToMove = categoryServices[sourceIndex];

        if (!serviceToMove) {
          console.error('Failed to find service to move');
          return;
        }

        console.log(`Reordering service: ${serviceToMove.id} from index ${sourceIndex} to ${targetIndex}`);

        // Reorder the items in the local array (proper reordering, not swapping)
        const reorderedServices = [...categoryServices];
        const [movedService] = reorderedServices.splice(sourceIndex, 1);
        reorderedServices.splice(targetIndex, 0, movedService);

        // Update the services by category
        currentServicesByCategory[sourceCategoryId] = reorderedServices;
        this.adminService.updateServicesByCategory(currentServicesByCategory);

        // Call the reorder API instead of swap
        this.adminService.reorderServiceInCategory(sourceCategoryId, sourceIndex, targetIndex);

        // Force update of the UI
        setTimeout(() => {
          if (callback) callback();
        }, 100);
      } else {
        // If source and target categories are different, move the service to the new category
        console.log(`Moving service from category ${sourceCategoryId}, index ${sourceIndex} to category ${targetCategoryId}, index ${targetIndex}`);

        this.adminService.moveServiceToCategory(sourceCategoryId, targetCategoryId, sourceIndex, targetIndex);

        // Force update of the UI
        setTimeout(() => {
          if (callback) callback();
        }, 100);
      }

      console.log('Service moved successfully');
    } catch (error) {
      console.error('Error handling service drop:', error);
    }
  }



  /**
   * Handles the drop of a category
   * @param sourceIndex The source index
   * @param targetIndex The target index
   * @param categories The categories array
   * @param callback Callback function to execute after completion
   */
  handleCategoryDrop(
    sourceIndex: number,
    targetIndex: number,
    categories: ExtendedCategoryRes[],
    callback?: (updatedCategories: ExtendedCategoryRes[]) => void
  ): void {
    console.log(`Reordering categories from index ${sourceIndex} to index ${targetIndex}`);

    try {
      // If source and target indices are the same, no need to reorder
      if (sourceIndex === targetIndex) {
        console.log('Source and target indices are the same, no need to reorder');
        if (callback) callback(categories);
        return;
      }

      // Get a copy of the current categories
      const categoriesCopy = [...categories];

      // Validate indices
      if (sourceIndex < 0 || sourceIndex >= categoriesCopy.length) {
        console.error(`Invalid source index: ${sourceIndex}, max: ${categoriesCopy.length - 1}`);
        return;
      }

      if (targetIndex < 0 || targetIndex >= categoriesCopy.length) {
        console.error(`Invalid target index: ${targetIndex}, max: ${categoriesCopy.length - 1}`);
        return;
      }

      // Get the category being moved
      const categoryToMove = categoriesCopy[sourceIndex];

      console.log(`Reordering category: ${categoryToMove.id} (${categoryToMove.name}) from index ${sourceIndex} to ${targetIndex}`);

      // Reorder the items in the local array (proper reordering, not swapping)
      const [movedCategory] = categoriesCopy.splice(sourceIndex, 1);
      categoriesCopy.splice(targetIndex, 0, movedCategory);

      // Update the servicesByCategory in AdminServiceService
      this.categoryService.updateServicesByCategoryOrder(categoriesCopy);

      // Call the API to reorder category sort order
      this.adminService.reorderCategorySort(sourceIndex, targetIndex, categoriesCopy).subscribe({
        next: () => {
          // Log the new category order for debugging
          console.log('Categories reordered successfully');
          console.log('New category order:', categoriesCopy.map(c => `${c.name} (${c.id})`));

          if (callback) callback(categoriesCopy);
        },
        error: (error) => {
          console.error('Error reordering categories:', error);
          // Revert the local changes on error
          if (callback) callback(categories);
        }
      });
    } catch (error) {
      console.error('Error handling category drop:', error);
    }
  }

  /**
   * Finds a service's index in a category
   * @param categoryId The category ID
   * @param serviceId The service ID
   * @param displayCategories The display categories array
   * @returns The index of the service in the category, or -1 if not found
   */
  findServiceIndex(categoryId: string, serviceId: number, displayCategories: ExtendedCategoryRes[]): number {
    const category = this.findCategoryById(categoryId, displayCategories);
    if (!category) return -1;

    return category.services.findIndex(service => service.id === serviceId);
  }

  /**
   * Finds a category by ID
   * @param categoryId The category ID
   * @param displayCategories The display categories array
   * @returns The category, or undefined if not found
   */
  findCategoryById(categoryId: string, displayCategories: ExtendedCategoryRes[]): ExtendedCategoryRes | undefined {
    return displayCategories.find(category => category.id.toString() === categoryId);
  }

  /**
   * Moves selected services to a new category
   * @param selectedServices The selected service IDs
   * @param targetCategory The target category
   * @param displayCategories The display categories array
   * @param callback Callback function to execute after completion
   */
  moveServicesToCategory(
    selectedServices: Set<number>,
    targetCategory: any,
    displayCategories: ExtendedCategoryRes[],
    callback?: () => void
  ): void {
    const selectedServiceIds = Array.from(selectedServices);

    // Find the services to move
    const servicesToMove: { service: SuperGeneralSvRes, categoryId: string }[] = [];

    displayCategories.forEach(category => {
      category.services.forEach(service => {
        if (selectedServiceIds.includes(service.id)) {
          servicesToMove.push({
            service,
            categoryId: category.id.toString()
          });
        }
      });
    });

    // Move each service to the new category
    servicesToMove.forEach(item => {
      const sourceCategory = item.categoryId;
      const targetCategoryId = targetCategory.id.toString();
      const sourceIndex = this.findServiceIndex(sourceCategory, item.service.id, displayCategories);

      if (sourceIndex !== -1) {
        // Move the service to the end of the target category
        const targetIndex = this.findCategoryById(targetCategoryId, displayCategories)?.services.length || 0;
        this.adminService.moveServiceToCategory(sourceCategory, targetCategoryId, sourceIndex, targetIndex);
      }
    });

    // Refresh the display
    setTimeout(() => {
      if (callback) callback();
    }, 100);
  }
}
