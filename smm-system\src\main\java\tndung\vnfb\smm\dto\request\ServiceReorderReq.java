package tndung.vnfb.smm.dto.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ServiceReorderReq {
    
    @NotEmpty(message = "Services list cannot be empty")
    @Valid
    private List<ServiceOrderItem> services;
    
    @Data
    public static class ServiceOrderItem {
        @NotNull(message = "Service ID cannot be null")
        private Long id;
        
        @NotNull(message = "Sort order cannot be null")
        private Integer sort;
    }
}
