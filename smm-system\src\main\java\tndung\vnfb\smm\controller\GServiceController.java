package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.ServiceReq;
import tndung.vnfb.smm.dto.request.ServiceReorderReq;
import tndung.vnfb.smm.dto.response.service.ServiceRes;
import tndung.vnfb.smm.dto.response.service.SuperServiceRes;
import tndung.vnfb.smm.dto.response.smm.SMMServiceRes;
import tndung.vnfb.smm.service.FavoriteServiceService;
import tndung.vnfb.smm.service.GSvService;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;



@RestController
@RequestMapping("/v1/services")
@RequiredArgsConstructor
public class GServiceController {

    private final GSvService gSvService;
    private final FavoriteServiceService favoriteService;

    @GetMapping()
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<ServiceRes>> getAll() {
        return ApiResponseEntity.success(gSvService.getAll());
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<SuperServiceRes> getById(@PathVariable Long id) {
        return ApiResponseEntity.success(gSvService.getResById(id));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> delete(@PathVariable Long id) {
        gSvService.delete(id);
        return ApiResponseEntity.success();
    }
    @PostMapping()
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ServiceRes> add(@RequestBody ServiceReq req) {
        return ApiResponseEntity.success(gSvService.add(req));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<ServiceRes> edit( @PathVariable Long id,@RequestBody ServiceReq req) {
        return ApiResponseEntity.success(gSvService.edit(id, req));
    }


    @PutMapping("/favorites/{id}")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL')")
    public ApiResponseEntity<String> addFavorite( @PathVariable Long id) {
        favoriteService.addFavorite(id);
        return ApiResponseEntity.success();
    }

    @DeleteMapping("/favorites/{id}")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL')")
    public ApiResponseEntity<String> removeFavorite( @PathVariable Long id) {
        favoriteService.removeFavorite(id);
        return ApiResponseEntity.success();
    }
    @GetMapping("/favorites")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL')")
    public ApiResponseEntity<List<Long>> getMyFavorites() {
        return ApiResponseEntity.success(favoriteService.getFavorite());
    }

//    @PutMapping("{id}")
//    @PreAuthorize("hasRole('ROLE_PANEL')")
//    public ApiResponseEntity<CategoryRes> edit(@PathVariable Integer id, @RequestBody ServiceEditReq req) {
//        return ApiResponseEntity.success(generalSvService.edit(id, req));
//    }

    @PutMapping("{id}/active")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<SuperServiceRes> active(@PathVariable Long id) {
        return ApiResponseEntity.success(gSvService.active(id));
    }

    @PutMapping("{id}/deactivate")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<SuperServiceRes> deactivate(@PathVariable Long id) {
        return ApiResponseEntity.success(gSvService.deactivate(id));
    }


    @GetMapping("/{id}/smm")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<List<SMMServiceRes>> getServicesByProvider(@PathVariable Long id) {
        return ApiResponseEntity.success(gSvService.getServicesByProvider(id));
    }

    @PatchMapping("/swap-sort")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<String> swapSort( @RequestParam Long id1,
                                               @RequestParam Long id2) {
        gSvService.swapSort(id1, id2);
        return ApiResponseEntity.success();
    }

    @PatchMapping("/reorder")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> reorder(@RequestBody @Valid ServiceReorderReq req) {
        gSvService.reorder(req);
        return ApiResponseEntity.success();
    }

    @PatchMapping("/{id}/price")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<String> changePrice(@PathVariable Long id, @RequestParam BigDecimal percent) {
        gSvService.changePrice(id, percent);
        return ApiResponseEntity.success();
    }

    @PutMapping("/{id}/categories")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<String> changeCategory(@PathVariable Long id,
                                                    @RequestParam Long newCategory)
    {
        gSvService.changeCategory(id,newCategory);
        return ApiResponseEntity.success();
    }

    @PutMapping("/categories/{categoryId}/price-sort")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<String> priceSort(
            @PathVariable Long categoryId, @RequestParam Sort.Direction direction)
    {
        gSvService.priceSort(categoryId,direction);
        return ApiResponseEntity.success();
    }

    @PostMapping("/{id}/duplicate")
    @PreAuthorize("hasRole('ROLE_PANEL')")
    public ApiResponseEntity<SuperServiceRes> duplicate(@PathVariable Long id)
    {

        return ApiResponseEntity.success(gSvService.duplicate(id));
    }
}
