package tndung.vnfb.smm.service;

import tndung.vnfb.smm.dto.request.CategoryReq;
import tndung.vnfb.smm.dto.request.CategoryReorderReq;
import tndung.vnfb.smm.dto.request.EditCategoryReq;
import tndung.vnfb.smm.dto.response.category.CategoryRes;
import tndung.vnfb.smm.entity.Category;

import java.util.List;

public interface CategoryService {
    CategoryRes deactivate(Long id);

    CategoryRes active(Long id);

    CategoryRes edit(Long id, EditCategoryReq req);

    Category findById(Long id);

    CategoryRes add(CategoryReq req);
    List<CategoryRes> getAll();

    List<CategoryRes> getByPlatform(Long platform);

    void swapSort(Long id1, Long id2);

    void reorder(CategoryReorderReq req);

    void changePlatform(Long categoryId, Long newPlatform);
}
