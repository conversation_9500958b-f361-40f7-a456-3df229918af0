package tndung.vnfb.smm.dto.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CategoryReorderReq {
    
    @NotEmpty(message = "Categories list cannot be empty")
    @Valid
    private List<CategoryOrderItem> categories;
    
    @Data
    public static class CategoryOrderItem {
        @NotNull(message = "Category ID cannot be null")
        private Long id;
        
        @NotNull(message = "Sort order cannot be null")
        private Integer sort;
    }
}
